# 支付渠道配置示例
# 使用方式：将此文件重命名为 application.yml 或在现有的 application.yml 中添加以下配置

payment:
  channels:
    # 智付渠道配置
    dinpay:
      enabled: true                           # 是否启用智付渠道
      merchant-no: "YOUR_MERCHANT_NO"         # 商户号
      secret: "YOUR_SECRET_KEY"               # 加密密钥
      public-key: "YOUR_PUBLIC_KEY_BASE64"    # 平台公钥(Base64)
      private-key: "YOUR_PRIVATE_KEY_BASE64"  # 商户私钥(Base64)
      environment: "dev"                      # 环境：dev(测试) 或 pro(生产)
      mini-app-id: "YOUR_MINI_APP_ID"         # 小程序AppID
      app-id: "YOUR_APP_ID"                   # 公众号/H5 AppID

    # 可以添加其他渠道配置
    # other-channel:
    #   enabled: false
    #   # 其他渠道的配置项...

# 日志配置（可选）
logging:
  level:
    net.summerfarm.payment.trade: DEBUG      # 支付模块日志级别
    net.summerfarm.payment.trade.config: INFO # 配置类日志级别
