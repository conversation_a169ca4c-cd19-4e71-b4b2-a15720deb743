package net.summerfarm.payment.trade.client.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.dto.*;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.common.crypto.CertUtils;
import net.summerfarm.payment.trade.common.crypto.SM2Utils;
import net.summerfarm.payment.trade.common.crypto.SM4Utils;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.common.util.DinUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;

/**
 * 智付（DinPay）渠道客户端
 * 负责与智付API进行实际的HTTP交互，包括加密、签名和验签
 */
@Slf4j
public class DinPayClient implements ChannelClient<DinPayRequestDTO, DinResponseDTO<DinPayResponseDTO>> {

    private static final String CHANNEL_CODE = "DINPAY";
    private static final String CHANNEL_NAME = "智付";
    private static final String SIGNATURE_METHOD = "SM3WITHSM2";

    private final DinPayConfig dinPayConfig;

    public DinPayClient(DinPayConfig dinPayConfig) {
        if (dinPayConfig == null) {
            throw new IllegalArgumentException("智付配置不能为空");
        }
        this.dinPayConfig = dinPayConfig;
        Security.addProvider(new BouncyCastleProvider());
    }

    @Override
    public String getChannelCode() {
        return CHANNEL_CODE;
    }

    @Override
    public String getChannelName() {
        return CHANNEL_NAME;
    }

    @Override
    public DinResponseDTO<DinPayResponseDTO> pay(DinPayRequestDTO channelRequest) throws Exception {
        log.info("Executing DinPay payment for order: {}", channelRequest.getOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getPayUrl());
        return dinCommonRequest(url, channelRequest, DinPayResponseDTO.class);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <QR, QResp> QResp queryPayment(QR channelQueryRequest) throws Exception {
        if (!(channelQueryRequest instanceof DinPayQueryRequestDTO)) {
            throw new IllegalArgumentException("Request must be of type DinPayQueryRequestDTO");
        }
        DinPayQueryRequestDTO request = (DinPayQueryRequestDTO) channelQueryRequest;
        log.info("Executing DinPay query for order: {}", request.getOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getQueryUrl());
        DinResponseDTO<DinPayQueryResponseDTO> response = dinCommonRequest(url, request, DinPayQueryResponseDTO.class);
        return (QResp) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <CR, CResp> CResp closePayment(CR channelCloseRequest) throws Exception {
        if (!(channelCloseRequest instanceof DinPayCloseRequestDTO)) {
            throw new IllegalArgumentException("Request must be of type DinPayCloseRequestDTO");
        }
        DinPayCloseRequestDTO request = (DinPayCloseRequestDTO) channelCloseRequest;
        log.info("Executing DinPay close for order: {}", request.getPayOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getCloseUrl());
        DinResponseDTO<DinPayCloseResponseDTO> response = dinCommonRequest(url, request, DinPayCloseResponseDTO.class);
        return (CResp) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <R, RResp> RResp refund(R channelRefundRequest) throws Exception {
        if (!(channelRefundRequest instanceof DinRefundRequestDTO)) {
            throw new IllegalArgumentException("Request must be of type DinRefundRequestDTO");
        }
        DinRefundRequestDTO request = (DinRefundRequestDTO) channelRefundRequest;
        log.info("Executing DinPay refund for order: {}", request.getRefundOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getRefundUrl());
        DinResponseDTO<DinRefundResponseDTO> response = dinCommonRequest(url, request, DinRefundResponseDTO.class);
        return (RResp) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <QR, QResp> QResp queryRefund(QR channelQueryRefundRequest) throws Exception {
        if (!(channelQueryRefundRequest instanceof DinRefundQueryRequestDTO)) {
            throw new IllegalArgumentException("Request must be of type DinRefundQueryRequestDTO");
        }
        DinRefundQueryRequestDTO request = (DinRefundQueryRequestDTO) channelQueryRefundRequest;
        log.info("Executing DinPay query refund for order: {}", request.getRefundOrderNo());
        String url = dinPayConfig.getDomain().concat(dinPayConfig.getQueryRefundUrl());
        DinResponseDTO<DinRefundQueryResponseDTO> response = dinCommonRequest(url, request, DinRefundQueryResponseDTO.class);
        return (QResp) response;
    }

    private <T, R> DinResponseDTO<R> dinCommonRequest(String url, T requestData, Class<R> responseClass) {
        PrivateKey merchantPrivateKey = CertUtils.getPrivateKeyByBase64(dinPayConfig.getPrivateKey());
        PublicKey platformPublicKey = CertUtils.getPublicKeyByBase64(dinPayConfig.getPublicKey());
        String encryptionKey = dinPayConfig.getSecret();
        String encryptedEncryptionKey = SM2Utils.encryptToBase64(platformPublicKey, encryptionKey);
        String requestJson = JSON.toJSONString(requestData);
        log.debug("DinPay business request params: {}", requestJson);
        String encryptedData = SM4Utils.encryptBase64(requestJson, encryptionKey);
        String signature = SM2Utils.sign(merchantPrivateKey, encryptedData);
        DinRequestDTO dinRequest = new DinRequestDTO(dinPayConfig.getMerchantNo(), encryptedEncryptionKey, SIGNATURE_METHOD, signature, String.format("%014d", System.currentTimeMillis()), encryptedData);
        String requestBody = JSON.toJSONString(dinRequest);
        DinResponseDTO<R> response = DinUtils.executeRequest(url, requestBody, responseClass, platformPublicKey);
        checkDinResponse(response);
        return response;
    }

    private void checkDinResponse(DinResponseDTO<?> response) {
        if (!"0000".equals(response.getCode())) {
            log.error("DinPay request failed with code: {}, message: {}", response.getCode(), response.getMsg());
            throw new PaymentException(ErrorCode.CHANNEL_UNAVAILABLE, String.format("渠道返回错误: code=%s, msg=%s", response.getCode(), response.getMsg()));
        }
        if (response.getData() == null) {
            log.error("DinPay response data is null. Response: {}", response);
            throw new PaymentException(ErrorCode.CHANNEL_UNAVAILABLE, "智付响应数据为空");
        }
    }
}