package net.summerfarm.payment.trade.config;

import net.summerfarm.payment.trade.adapter.dinpay.DinPayAdapter;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.client.impl.DinPayClient;
import net.summerfarm.payment.trade.common.enums.DinPaymentEnum;
import net.summerfarm.payment.trade.service.impl.PaymentClientServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 基于配置文件的渠道配置类
 * 从application.yml读取渠道配置信息
 * 
 * 使用方式：在application.yml中添加以下配置
 * 
 * payment:
 *   channels:
 *     dinpay:
 *       enabled: true
 *       merchant-no: YOUR_MERCHANT_NO
 *       secret: YOUR_SECRET_KEY
 *       public-key: YOUR_PUBLIC_KEY_BASE64
 *       private-key: YOUR_PRIVATE_KEY_BASE64
 *       environment: dev  # dev 或 pro
 *       mini-app-id: YOUR_MINI_APP_ID
 *       app-id: YOUR_APP_ID
 * 
 * <AUTHOR> Agent
 * @date 2025-08-19
 */
@Configuration
@ConditionalOnProperty(prefix = "payment.channels.dinpay", name = "enabled", havingValue = "true")
public class PropertyBasedChannelConfiguration {

    private static final Logger log = LoggerFactory.getLogger(PropertyBasedChannelConfiguration.class);

    @Autowired
    private PaymentClientServiceImpl paymentClientService;

    // 智付配置属性
    @Value("${payment.channels.dinpay.merchant-no}")
    private String dinpayMerchantNo;

    @Value("${payment.channels.dinpay.secret}")
    private String dinpaySecret;

    @Value("${payment.channels.dinpay.public-key}")
    private String dinpayPublicKey;

    @Value("${payment.channels.dinpay.private-key}")
    private String dinpayPrivateKey;

    @Value("${payment.channels.dinpay.environment:dev}")
    private String dinpayEnvironment;

    @Value("${payment.channels.dinpay.mini-app-id}")
    private String dinpayMiniAppId;

    @Value("${payment.channels.dinpay.app-id}")
    private String dinpayAppId;

    /**
     * 注册基于配置文件的支付渠道
     */
    @PostConstruct
    public void registerPropertyBasedChannels() {
        log.info("开始注册基于配置文件的支付渠道...");
        
        // 注册智付渠道
        registerDinPayChannel();
        
        log.info("基于配置文件的支付渠道注册完成");
    }

    /**
     * 注册智付渠道
     */
    private void registerDinPayChannel() {
        try {
            // 创建智付配置
            DinPayConfig dinPayConfig = createDinPayConfigFromProperties();
            
            // 创建适配器和客户端
            DinPayAdapter adapter = new DinPayAdapter();
            DinPayClient client = new DinPayClient(dinPayConfig);
            
            // 注册到服务中
            paymentClientService.registerAdapter(adapter);
            paymentClientService.registerClient(client);
            
            log.info("智付渠道注册成功: channelCode={}, channelName={}, environment={}", 
                    adapter.getChannelCode(), adapter.getChannelName(), dinpayEnvironment);
        } catch (Exception e) {
            log.error("智付渠道注册失败", e);
        }
    }

    /**
     * 从配置文件创建智付配置
     */
    private DinPayConfig createDinPayConfigFromProperties() {
        DinPayConfig config = new DinPayConfig();
        
        // 基础配置
        config.setMerchantNo(dinpayMerchantNo);
        config.setSecret(dinpaySecret);
        config.setPublicKey(dinpayPublicKey);
        config.setPrivateKey(dinpayPrivateKey);
        
        // 环境配置
        boolean isProd = "pro".equalsIgnoreCase(dinpayEnvironment);
        config.setDomain(DinPaymentEnum.domain.determineDomain(isProd));
        
        // API路径配置（使用枚举避免魔法值）
        config.setPayUrl(DinPaymentEnum.url.MINI_APP_PAY.getUrl());
        config.setQueryUrl(DinPaymentEnum.url.PAY_QUERY.getUrl());
        config.setCloseUrl(DinPaymentEnum.url.PAY_CLOSE.getUrl());
        config.setRefundUrl(DinPaymentEnum.url.REFUND.getUrl());
        config.setQueryRefundUrl(DinPaymentEnum.url.REFUND_QUERY.getUrl());
        
        // 应用配置
        config.setMiniAppId(dinpayMiniAppId);
        config.setAppId(dinpayAppId);
        
        return config;
    }
}
