package net.summerfarm.payment.trade.config;

import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.service.impl.PaymentClientServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 支付交易配置类
 * 自动注册所有的适配器和客户端
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Configuration
public class PaymentTradeConfiguration {

    private static final Logger log = LoggerFactory.getLogger(PaymentTradeConfiguration.class);

    @Autowired
    private PaymentClientServiceImpl paymentClientService;

    @Autowired(required = false)
    private List<ChannelAdapter> channelAdapters;

    @Autowired(required = false)
    private List<ChannelClient> channelClients;

    /**
     * 智付适配器Bean配置
     */
    @Bean
    public DinPayAdapter dinPayAdapter() {
        return new DinPayAdapter();
    }

    /**
     * 智付客户端Bean配置
     * 注意：这里使用默认配置，实际使用时需要从配置文件或数据库获取真实配置
     */
    @Bean
    public DinPayClient dinPayClient() {
        // 创建默认配置（实际使用时应该从配置文件或数据库获取）
        DinPayConfig defaultConfig = createDefaultDinPayConfig();
        return new DinPayClient(defaultConfig);
    }

    /**
     * 创建默认的智付配置
     * 实际使用时应该从配置文件、环境变量或数据库获取
     */
    private DinPayConfig createDefaultDinPayConfig() {
        DinPayConfig config = new DinPayConfig();
        // 设置默认值，实际使用时需要替换为真实配置
        config.setMerchantNo("DEFAULT_MERCHANT");
        config.setSecret("DEFAULT_SECRET");
        config.setPublicKey("DEFAULT_PUBLIC_KEY");
        config.setPrivateKey("DEFAULT_PRIVATE_KEY");
        config.setDomain("https://paymenttest.dinpay.com/trx");
        config.setPayUrl("/api/appPay/payApplet");
        config.setQueryUrl("/api/appPay/payQuery");
        config.setCloseUrl("/api/appPay/payClose");
        config.setRefundUrl("/api/appPay/payRefund");
        config.setQueryRefundUrl("/api/appPay/payRefundQuery");
        config.setMiniAppId("DEFAULT_MINI_APP_ID");
        config.setAppId("DEFAULT_APP_ID");
        return config;
    }

    /**
     * 自动注册所有的适配器和客户端
     */
    @PostConstruct
    public void registerChannelComponents() {
        log.info("开始注册支付渠道组件...");

        // 1. 注册Spring管理的适配器和客户端
        registerSpringManagedComponents();

        // 2. 注册固定的渠道组件（简化版）
        registerFixedChannelComponents();

        // 验证适配器和客户端的匹配性
        validateChannelComponents();

        log.info("支付渠道组件注册完成");
    }

    /**
     * 注册Spring管理的组件
     */
    private void registerSpringManagedComponents() {
        // 注册适配器
        if (channelAdapters != null && !channelAdapters.isEmpty()) {
            for (ChannelAdapter adapter : channelAdapters) {
                try {
                    paymentClientService.registerAdapter(adapter);
                } catch (Exception e) {
                    log.error("注册适配器失败: channelCode={}, error={}",
                            adapter.getChannelCode(), e.getMessage(), e);
                }
            }
            log.info("成功注册 {} 个Spring管理的渠道适配器", channelAdapters.size());
        }

        // 注册客户端
        if (channelClients != null && !channelClients.isEmpty()) {
            for (ChannelClient client : channelClients) {
                try {
                    paymentClientService.registerClient(client);
                } catch (Exception e) {
                    log.error("注册客户端失败: channelCode={}, error={}",
                            client.getChannelCode(), e.getMessage(), e);
                }
            }
            log.info("成功注册 {} 个Spring管理的渠道客户端", channelClients.size());
        }
    }

    /**
     * 注册固定的渠道组件（简化版）
     * 这种方式适合渠道数量较少且相对固定的场景
     */
    private void registerFixedChannelComponents() {
        try {
            // 注册智付适配器和客户端
            DinPayAdapter dinPayAdapter = dinPayAdapter();
            DinPayClient dinPayClient = dinPayClient();

            paymentClientService.registerAdapter(dinPayAdapter);
            paymentClientService.registerClient(dinPayClient);

            log.info("成功注册固定渠道组件: {}", dinPayAdapter.getChannelCode());
        } catch (Exception e) {
            log.error("注册固定渠道组件失败", e);
        }
    }

    /**
     * 验证适配器和客户端的匹配性
     */
    private void validateChannelComponents() {
        if (channelAdapters == null || channelClients == null) {
            return;
        }

        for (ChannelAdapter adapter : channelAdapters) {
            String channelCode = adapter.getChannelCode();
            boolean hasMatchingClient = channelClients.stream()
                    .anyMatch(client -> channelCode.equals(client.getChannelCode()));

            if (!hasMatchingClient) {
                log.warn("适配器 {} 没有对应的客户端实现", channelCode);
            }
        }

        for (ChannelClient client : channelClients) {
            String channelCode = client.getChannelCode();
            boolean hasMatchingAdapter = channelAdapters.stream()
                    .anyMatch(adapter -> channelCode.equals(adapter.getChannelCode()));

            if (!hasMatchingAdapter) {
                log.warn("客户端 {} 没有对应的适配器实现", channelCode);
            }
        }
    }
}
