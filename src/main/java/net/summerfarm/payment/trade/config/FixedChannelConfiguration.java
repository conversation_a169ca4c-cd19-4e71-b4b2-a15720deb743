package net.summerfarm.payment.trade.config;

import net.summerfarm.payment.trade.adapter.dinpay.DinPayAdapter;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.client.impl.DinPayClient;
import net.summerfarm.payment.trade.common.enums.DinPaymentEnum;
import net.summerfarm.payment.trade.service.impl.PaymentClientServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 固定渠道配置类
 * 简化版的渠道注册，直接硬编码渠道信息
 * 适合渠道数量较少且相对固定的场景
 * 
 * <AUTHOR> Agent
 * @date 2025-08-19
 */
@Configuration
public class FixedChannelConfiguration {

    private static final Logger log = LoggerFactory.getLogger(FixedChannelConfiguration.class);

    @Autowired
    private PaymentClientServiceImpl paymentClientService;

    /**
     * 注册固定的支付渠道
     */
    @PostConstruct
    public void registerFixedChannels() {
        log.info("开始注册固定支付渠道...");
        
        // 注册智付渠道
        registerDinPayChannel();
        
        // 可以在这里添加其他固定渠道
        // registerOtherChannel();
        
        log.info("固定支付渠道注册完成");
    }

    /**
     * 注册智付渠道
     */
    private void registerDinPayChannel() {
        try {
            // 创建智付配置
            DinPayConfig dinPayConfig = createDinPayConfig();
            
            // 创建适配器和客户端
            DinPayAdapter adapter = new DinPayAdapter();
            DinPayClient client = new DinPayClient(dinPayConfig);
            
            // 注册到服务中
            paymentClientService.registerAdapter(adapter);
            paymentClientService.registerClient(client);
            
            log.info("智付渠道注册成功: channelCode={}, channelName={}", 
                    adapter.getChannelCode(), adapter.getChannelName());
        } catch (Exception e) {
            log.error("智付渠道注册失败", e);
        }
    }

    /**
     * 创建智付配置
     * 这里使用固定配置，实际使用时可以：
     * 1. 从application.yml配置文件读取
     * 2. 从环境变量读取
     * 3. 从数据库读取
     * 4. 从配置中心读取
     */
    private DinPayConfig createDinPayConfig() {
        DinPayConfig config = new DinPayConfig();
        
        // 基础配置
        config.setMerchantNo("YOUR_MERCHANT_NO");
        config.setSecret("YOUR_SECRET_KEY");
        config.setPublicKey("YOUR_PUBLIC_KEY_BASE64");
        config.setPrivateKey("YOUR_PRIVATE_KEY_BASE64");
        
        // 环境配置（测试环境）
        config.setDomain(DinPaymentEnum.domain.DEV.getDomain());
        
        // API路径配置（使用枚举避免魔法值）
        config.setPayUrl(DinPaymentEnum.url.MINI_APP_PAY.getUrl());
        config.setQueryUrl(DinPaymentEnum.url.PAY_QUERY.getUrl());
        config.setCloseUrl(DinPaymentEnum.url.PAY_CLOSE.getUrl());
        config.setRefundUrl(DinPaymentEnum.url.REFUND.getUrl());
        config.setQueryRefundUrl(DinPaymentEnum.url.REFUND_QUERY.getUrl());
        
        // 应用配置
        config.setMiniAppId("YOUR_MINI_APP_ID");
        config.setAppId("YOUR_APP_ID");
        
        return config;
    }

    /**
     * 示例：注册其他渠道的方法
     * 可以按照类似的模式添加其他支付渠道
     */
    /*
    private void registerOtherChannel() {
        try {
            // 创建其他渠道的配置
            OtherChannelConfig config = createOtherChannelConfig();
            
            // 创建适配器和客户端
            OtherChannelAdapter adapter = new OtherChannelAdapter();
            OtherChannelClient client = new OtherChannelClient(config);
            
            // 注册到服务中
            paymentClientService.registerAdapter(adapter);
            paymentClientService.registerClient(client);
            
            log.info("其他渠道注册成功: channelCode={}", adapter.getChannelCode());
        } catch (Exception e) {
            log.error("其他渠道注册失败", e);
        }
    }
    */
}
