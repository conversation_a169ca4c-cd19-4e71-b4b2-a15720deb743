package net.summerfarm.payment.trade.model.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 通用渠道配置模型
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道标识符（如：wechat_native, dinpay, cmb等）
     */
    private String channelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号（可选）
     */
    private String subMerchantNo;

    /**
     * 应用ID（如微信的appId）
     */
    private String appId;

    /**
     * 应用密钥（如微信的appSecret）
     */
    private String appSecret;

    /**
     * 商户密钥/签名密钥
     */
    private String secretKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 证书路径或证书内容
     */
    private String certificate;

    /**
     * 渠道特有的扩展配置
     * 例如：DinPay的特殊参数、招行的收银员ID等
     */
    private Map<String, Object> extraConfig;

    /**
     * 渠道特定的配置对象
     * 例如：DinPayConfig、WechatConfig等
     */
    private Object channelSpecificConfig;


    /**
     * 获取扩展配置值
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @param <T> 值类型
     * @return 配置值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtraConfig(String key, T defaultValue) {
        if (extraConfig == null || !extraConfig.containsKey(key)) {
            return defaultValue;
        }
        try {
            return (T) extraConfig.get(key);
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * 获取扩展配置值
     *
     * @param key 配置键
     * @param <T> 值类型
     * @return 配置值，可能为null
     */
    public <T> T getExtraConfig(String key) {
        return getExtraConfig(key, null);
    }

    /**
     * 设置扩展配置值
     *
     * @param key 配置键
     * @param value 配置值
     */
    public void setExtraConfig(String key, Object value) {
        if (extraConfig == null) {
            extraConfig = new java.util.HashMap<>();
        }
        extraConfig.put(key, value);
    }
}
