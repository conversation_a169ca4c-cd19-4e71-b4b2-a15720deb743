package net.summerfarm.payment.trade.model.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 支付凭证信息
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentCredential implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 凭证内容（二维码内容、跳转URL、表单HTML等）
     */
    private String content;

    /**
     * 凭证过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 额外的凭证信息
     */
    private Map<String, Object> extraData;
}
