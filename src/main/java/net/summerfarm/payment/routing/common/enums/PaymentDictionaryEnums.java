package net.summerfarm.payment.routing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-12-10
 **/
public class PaymentDictionaryEnums {

    @Getter
    @AllArgsConstructor
    public enum ChannelName {

        WECHAT(0, "微信原生"),
        @Deprecated
        BOC(1, "中国银行"),
        CMB(2,"招行间连"),
        B2B_WECHAT_FIRE_FACE(3,"微信B2B支付"),
        DIN_PAY(4, "智付间联"),
        HUI_FU(5, "汇付间联"),
        ;

        private final Integer code;
        private final String name;

        public static Integer getCodeByName(String name) {
            for (ChannelName channelName : ChannelName.values()) {
                if (channelName.getName().equals(name)) {
                    return channelName.getCode();
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum Platform {
        MINI_APP(0, "miniapp"),
        H5(1, "h5"),
        ;

        /**
         * 平台编码
         */
        private Integer code;

        private String name;
    }


}
