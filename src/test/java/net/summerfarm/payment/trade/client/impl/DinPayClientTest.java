package net.summerfarm.payment.trade.client.impl;

import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayRequestDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @description: DinPayClient测试类
 * @author: Augment Agent
 * @date: 2025-08-19
 **/
class DinPayClientTest {

    private DinPayClient dinPayClient;
    private DinPayConfig dinPayConfig;

    @BeforeEach
    void setUp() {
        dinPayConfig = new DinPayConfig();
        dinPayConfig.setMerchantNo("TEST_MERCHANT");
        dinPayConfig.setPrivateKey("TEST_PRIVATE_KEY");
        dinPayConfig.setPublicKey("TEST_PUBLIC_KEY");
        dinPayConfig.setSecret("TEST_SECRET");
        dinPayConfig.setDomain("https://test.dinpay.com");
        dinPayConfig.setPayUrl("/api/pay");
        dinPayConfig.setQueryUrl("/api/query");
        dinPayConfig.setCloseUrl("/api/close");
        dinPayConfig.setRefundUrl("/api/refund");
        dinPayConfig.setQueryRefundUrl("/api/queryRefund");

        dinPayClient = new DinPayClient();
        dinPayClient.setDinPayConfig(dinPayConfig);
    }

    @Test
    void testSetConfig_WithNullConfig_ThrowsChineseException() {
        // Given
        DinPayClient client = new DinPayClient();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            client.setDinPayConfig(null);
        });

        assertEquals("智付配置不能为空", exception.getMessage());
    }

    @Test
    void testPayment_WithoutConfig_ThrowsException() {
        // Given
        DinPayClient client = new DinPayClient();

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            client.queryPayment("invalid request");
        });

        assertEquals("智付配置未初始化，请先调用setDinPayConfig方法设置配置", exception.getMessage());
    }

    @Test
    void testGetChannelCode() {
        assertEquals("DINPAY", dinPayClient.getChannelCode());
    }

    @Test
    void testGetChannelName() {
        assertEquals("智付", dinPayClient.getChannelName());
    }

    @Test
    void testQueryPayment_WithInvalidRequest_ThrowsChineseException() {
        // Given
        String invalidRequest = "invalid request";

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            dinPayClient.queryPayment(invalidRequest);
        });

        assertEquals("请求参数必须是DinPayQueryRequestDTO类型", exception.getMessage());
    }

    @Test
    void testClosePayment_WithInvalidRequest_ThrowsChineseException() {
        // Given
        String invalidRequest = "invalid request";

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            dinPayClient.closePayment(invalidRequest);
        });

        assertEquals("请求参数必须是DinPayCloseRequestDTO类型", exception.getMessage());
    }

    @Test
    void testRefund_WithInvalidRequest_ThrowsChineseException() {
        // Given
        String invalidRequest = "invalid request";

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            dinPayClient.refund(invalidRequest);
        });

        assertEquals("请求参数必须是DinRefundRequestDTO类型", exception.getMessage());
    }

    @Test
    void testQueryRefund_WithInvalidRequest_ThrowsChineseException() {
        // Given
        String invalidRequest = "invalid request";

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            dinPayClient.queryRefund(invalidRequest);
        });

        assertEquals("请求参数必须是DinRefundQueryRequestDTO类型", exception.getMessage());
    }

    /**
     * 注意：由于DinPayClient的实际API调用需要真实的加密和网络请求，
     * 这里只测试了参数验证和异常处理的中文化。
     * 实际的API调用测试需要mock相关的加密工具类和网络请求。
     */
}
