package net.summerfarm.payment.trade.service;

import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.common.enums.DinPaymentEnum;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 支付服务使用示例
 * 展示如何使用外部配置进行支付
 *
 * <AUTHOR> Agent
 * @date 2025-08-19
 */
@Service
public class PaymentServiceUsageExample {

    @Autowired
    private PaymentClientService paymentClientService;

    /**
     * 示例：使用外部传入的配置进行支付
     */
    public UnifiedPaymentResult processPaymentWithExternalConfig(
            String merchantNo,
            String secret,
            String publicKey,
            String privateKey,
            String paymentNo,
            Long totalAmount) {

        // 1. 创建智付配置对象（由外部传入参数）
        DinPayConfig dinPayConfig = new DinPayConfig();
        dinPayConfig.setMerchantNo(merchantNo);
        dinPayConfig.setSecret(secret);
        dinPayConfig.setPublicKey(publicKey);
        dinPayConfig.setPrivateKey(privateKey);

        // 2. 设置环境配置（使用枚举避免魔法值）
        dinPayConfig.setDomain(DinPaymentEnum.domain.DEV.getDomain());
        dinPayConfig.setPayUrl(DinPaymentEnum.url.MINI_APP_PAY.getUrl());
        dinPayConfig.setQueryUrl(DinPaymentEnum.url.PAY_QUERY.getUrl());
        dinPayConfig.setCloseUrl(DinPaymentEnum.url.PAY_CLOSE.getUrl());
        dinPayConfig.setRefundUrl(DinPaymentEnum.url.REFUND.getUrl());
        dinPayConfig.setQueryRefundUrl(DinPaymentEnum.url.REFUND_QUERY.getUrl());

        // 3. 设置应用配置
        dinPayConfig.setMiniAppId("wx1234567890");
        dinPayConfig.setAppId("wx0987654321");

        // 4. 创建渠道配置，将DinPayConfig作为channelSpecificConfig传入
        ChannelConfig channelConfig = ChannelConfig.builder()
                .channelCode("DINPAY")
                .channelSpecificConfig(dinPayConfig)  // 关键：外部配置通过这里传入
                .build();

        // 5. 创建统一支付请求
        UnifiedPaymentRequest paymentRequest = UnifiedPaymentRequest.builder()
                .paymentNo(paymentNo)
                .totalAmount(totalAmount.intValue())
                .subject("测试商品")
                .body("测试商品描述")
                .platform("miniapp")
                .paymentMethod("wechat")
                .channelConfig(channelConfig)
                .build();

        // 6. 执行支付（SDK会自动将配置设置到DinPayClient中）
        return paymentClientService.pay(paymentRequest);
    }

    /**
     * 示例：多租户场景，根据租户ID获取不同的配置
     */
    public UnifiedPaymentResult processPaymentForTenant(Long tenantId, String paymentNo, Long totalAmount) {
        // 1. 根据租户ID获取配置（从数据库、配置中心等）
        DinPayConfig tenantConfig = getTenantDinPayConfig(tenantId);

        // 2. 创建渠道配置
        ChannelConfig channelConfig = ChannelConfig.builder()
                .channelCode("DINPAY")
                .channelSpecificConfig(tenantConfig)
                .build();

        // 3. 创建支付请求
        UnifiedPaymentRequest paymentRequest = UnifiedPaymentRequest.builder()
                .paymentNo(paymentNo)
                .totalAmount(totalAmount.intValue())
                .subject("租户商品")
                .body("租户商品描述")
                .platform("miniapp")
                .paymentMethod("wechat")
                .channelConfig(channelConfig)
                .build();

        // 4. 执行支付
        return paymentClientService.pay(paymentRequest);
    }

    /**
     * 模拟从数据库或配置中心获取租户配置
     */
    private DinPayConfig getTenantDinPayConfig(Long tenantId) {
        // 实际实现中，这里应该从数据库或配置中心获取
        DinPayConfig config = new DinPayConfig();

        // 根据租户ID设置不同的配置
        if (tenantId.equals(1001L)) {
            config.setMerchantNo("TENANT_1001_MERCHANT");
            config.setSecret("TENANT_1001_SECRET");
        } else if (tenantId.equals(1002L)) {
            config.setMerchantNo("TENANT_1002_MERCHANT");
            config.setSecret("TENANT_1002_SECRET");
        } else {
            config.setMerchantNo("DEFAULT_MERCHANT");
            config.setSecret("DEFAULT_SECRET");
        }

        // 公共配置
        config.setDomain(DinPaymentEnum.domain.DEV.getDomain());
        config.setPayUrl(DinPaymentEnum.url.MINI_APP_PAY.getUrl());
        config.setQueryUrl(DinPaymentEnum.url.PAY_QUERY.getUrl());
        config.setCloseUrl(DinPaymentEnum.url.PAY_CLOSE.getUrl());
        config.setRefundUrl(DinPaymentEnum.url.REFUND.getUrl());
        config.setQueryRefundUrl(DinPaymentEnum.url.REFUND_QUERY.getUrl());

        return config;
    }

    /**
     * 使用说明：
     *
     * 1. 配置外部化：
     *    - DinPayConfig由外部创建并传入
     *    - 支持从数据库、配置文件、配置中心等多种来源获取
     *    - 支持多租户场景，每个租户可以有不同的配置
     *
     * 2. 自动配置设置：
     *    - 将DinPayConfig作为ChannelConfig的channelSpecificConfig传入
     *    - SDK会自动识别并设置到对应的DinPayClient中
     *    - 无需手动调用setDinPayConfig方法
     *
     * 3. 类型安全：
     *    - 使用DinPaymentEnum避免魔法值
     *    - 编译时检查，减少运行时错误
     *
     * 4. 简化使用：
     *    - 业务代码只需关注配置的创建和传入
     *    - SDK内部处理所有的配置设置和调用逻辑
     */
}
