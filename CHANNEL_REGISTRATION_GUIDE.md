# 支付渠道注册方式指南

## 概述

目前支付SDK提供了多种渠道注册方式，从简单的固定配置到灵活的动态配置，可以根据不同的使用场景选择合适的方式。

## 注册方式对比

### 1. 原始方式：Spring自动装配 (PaymentTradeConfiguration)

**特点：**
- 通过Spring的`@Autowired`自动发现所有`ChannelAdapter`和`ChannelClient`的Bean
- 需要为每个渠道创建对应的`@Component`或`@Bean`

**优点：**
- 符合Spring的依赖注入原则
- 支持动态发现新增的渠道

**缺点：**
- 需要为每个渠道编写Bean配置
- 配置相对复杂

**使用场景：**
- 渠道数量较多且经常变化
- 需要高度的灵活性和扩展性

### 2. 简化方式：固定渠道注册 (FixedChannelConfiguration)

**特点：**
- 直接在配置类中硬编码渠道信息
- 一次性注册所有固定渠道

**优点：**
- 配置简单直观
- 无需额外的Bean配置
- 适合快速开发和测试

**缺点：**
- 渠道信息硬编码，不够灵活
- 新增渠道需要修改代码

**使用场景：**
- 渠道数量较少且相对固定
- 快速开发和原型验证

**代码示例：**
```java
@Configuration
public class FixedChannelConfiguration {
    
    @PostConstruct
    public void registerFixedChannels() {
        // 创建智付配置
        DinPayConfig config = createDinPayConfig();
        
        // 创建并注册适配器和客户端
        paymentClientService.registerAdapter(new DinPayAdapter());
        paymentClientService.registerClient(new DinPayClient(config));
    }
}
```

### 3. 推荐方式：基于配置文件 (PropertyBasedChannelConfiguration)

**特点：**
- 从`application.yml`读取渠道配置
- 支持条件化启用渠道
- 配置与代码分离

**优点：**
- 配置灵活，支持不同环境
- 无需修改代码即可调整配置
- 支持条件化启用/禁用渠道
- 便于运维管理

**缺点：**
- 需要预先定义配置结构
- 新增渠道类型仍需代码修改

**使用场景：**
- 生产环境部署
- 需要支持多环境配置
- 配置需要经常调整

**配置示例：**
```yaml
payment:
  channels:
    dinpay:
      enabled: true
      merchant-no: "YOUR_MERCHANT_NO"
      secret: "YOUR_SECRET_KEY"
      environment: "dev"
```

## 具体实现

### 方式1：使用固定渠道配置

1. **启用FixedChannelConfiguration**
   ```java
   // 在FixedChannelConfiguration中修改createDinPayConfig()方法
   // 填入真实的渠道配置信息
   ```

2. **禁用原始配置**（可选）
   ```java
   // 在PaymentTradeConfiguration上添加条件注解
   @ConditionalOnProperty(name = "payment.registration.mode", havingValue = "spring")
   ```

### 方式2：使用配置文件方式

1. **创建配置文件**
   ```yaml
   # application.yml
   payment:
     channels:
       dinpay:
         enabled: true
         merchant-no: "YOUR_MERCHANT_NO"
         # ... 其他配置
   ```

2. **启用PropertyBasedChannelConfiguration**
   - 该配置类会自动根据`payment.channels.dinpay.enabled`条件启用

### 方式3：混合使用

可以同时使用多种方式，系统会自动注册所有发现的渠道：

```java
@Configuration
public class HybridChannelConfiguration {
    
    @PostConstruct
    public void registerChannels() {
        // 1. 注册固定渠道
        registerFixedChannels();
        
        // 2. 注册基于配置文件的渠道
        registerPropertyBasedChannels();
        
        // 3. Spring自动装配的渠道会自动注册
    }
}
```

## 最佳实践建议

### 开发阶段
- 使用**固定渠道配置**进行快速开发和测试
- 配置简单，便于调试

### 测试阶段
- 使用**基于配置文件**的方式
- 支持不同测试环境的配置

### 生产阶段
- 使用**基于配置文件**的方式
- 配置外部化，便于运维管理
- 支持动态开启/关闭渠道

### 企业级应用
- 考虑从**数据库或配置中心**读取配置
- 支持动态配置更新
- 可以基于现有框架进行扩展

## 配置优先级

当多种配置方式同时存在时，注册顺序为：
1. Spring自动装配的Bean
2. 固定渠道配置
3. 基于配置文件的渠道

**注意：** 相同channelCode的渠道，后注册的会覆盖先注册的。

## 总结

- **快速开发**：选择固定渠道配置
- **生产部署**：选择基于配置文件
- **企业级**：考虑数据库或配置中心方案

根据您的具体需求选择合适的注册方式，也可以根据项目发展阶段逐步演进配置方式。
